#include "mod_overFreq.h"
/*过频保护*/

void runModOverFreq(s_modOverFreq* dp)
{
	unsigned char flg_con, flg_tmp;
	int fm;
    unsigned char flg_qd;

	fm = *(dp->fm) * DEFAULT_DIV_VAL;  // Assigning the value of dp->fm to fm
	// //闭锁条件
	// if(((*dp->u_max)<40*DEFAULT_DIV_VAL) | ((*dp->fm)<44.95))
	// {
	// 	dp->flg_blk = 1;
	// 	dp->timebuf_blk = 0;
	// }
	// else if(((*dp->u_max)>80*DEFAULT_DIV_VAL) & ((*dp->fm)>49.8))
	// {
	// 	 if(delayRelay(1, &dp->timebuf_blk, 1000))
	// 		dp->flg_blk = 0;
	// }
	//投退条件
	flg_con = dp->setSwit_of;
	
	flg_tmp = flg_con & (*dp->flg_abc);
	//频率大于过频定值	
	dp->qd_of = ((*(dp->fm)*DEFAULT_DIV_VAL)>dp->set_f_of_qd) & flg_tmp;

	if(((*(dp->fm)*DEFAULT_DIV_VAL)>dp->set_f_of_qd) && flg_tmp)
	{
		flg_qd = 1;
	}
	else
	{
		flg_qd = 0;
	}
	dp->qd_of = extRelay(flg_qd,&dp->timebuf_zk,500);

	//动作
	overRelay(&(dp->flg_of_op_val), &fm, dp->set_f_of, &(dp->timebuf_for_op), 0.95, 1);
	dp->op_of = delayRelay(dp->flg_of_op_val & dp->qd_of,&(dp->timebuf_op),dp->set_t_of);
	
	return;
}

int initModOverFreq(s_modOverFreq* dp, char* dpName)
{
	dp->dpName = dpName;
	
	//注册开关量
	regBI(dpName, "op_of", 0, &dp->op_of);	//动作信号
	// regBI(dpName, "ovbi_of", 0, &dp->ovbi_of);	
	//注册模拟量
	
	//注册启动元件
	regQdElement(&dp->qd_of);
	
	//注册动作元件
	regTripElement(&dp->op_of, &dp->trip_matrix);
	
	//加入队列
	addTaskLevel2(runModOverFreq, dp);
	
	return SUCC;
	
}
int initModParmOverFreq(s_modOverFreq* dp)
{
	//获取定值
	
	// dp->vbi_of = getSetByDesc("vbi_of");	//软压板
	// dp->ovbi_of = dp->vbi_of;
	dp->setSwit_of = getSetByDesc("setSwit_of");	//控制字
	dp->set_f_of = getSetByDesc( "set_f_of");	//过频定值
	dp->set_f_of_qd = dp->set_f_of+500;	//+0.05是门槛
	dp->set_t_of = getSetByDesc( "set_t_of"); //过频时间
	dp->trip_matrix = getSetByDesc("trip_matrix_of");
	return SUCC;
	
}