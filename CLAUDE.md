# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an embedded C project for an STM32F429 microcontroller-based protection device. The project uses the STM32 HAL (Hardware Abstraction Layer) library, LwIP TCP/IP stack, and implements various protection algorithms for electrical power systems.

## Build System

The project supports multiple build environments:
1. **CMake** - Primary build system with Makefiles generated in the `build/` directory
2. **Keil MDK-ARM** - Professional IDE project files in `MDK-ARM/` directory
3. **CLion** - CMake-based IDE support with configuration in `cmake-build-debug/`

### Build Commands

```bash
# Using CMake (MinGW Makefiles)
mkdir build && cd build
cmake .. -G "MinGW Makefiles"
make

# Rebuild the project
make clean && make

# Regenerate CMake build files
cmake --regenerate-during-build
```

### Compiler Information
- Toolchain: ARM GCC (MinGW64) or ARM Compiler 5 (Keil)
- Target: STM32F429xx (Cortex-M4 with FPU)
- C Standard: C99

## Project Structure

### Key Directories
- `Src/` - Main initialization files and peripheral drivers
- `Inc/` - Header files for main peripherals
- `Drivers/` - STM32 HAL drivers and CMSIS core files
- `Middlewares/` - Third-party libraries (LwIP TCP/IP stack)
- `source/` - Application source code organized by functionality
  - `main_task/` - Main application entry point and initialization
  - `mods/` - Protection modules (overcurrent, undervoltage, etc.)
  - `platform/` - Platform-specific components and tasks
  - `libexport/` - Hardware driver libraries
- `MDK-ARM/` - Keil uVision project files

### Key Files
- `main.c` - System entry point and HAL initialization
- `umain.c` - Main application loop and task scheduling
- `CMakeLists.txt` - CMake build configuration

## Architecture Overview

### Main Execution Flow
1. `main()` in `Src/main.c` - Hardware initialization and HAL setup
2. `umain()` in `source/main_task/umain.c` - Application initialization
3. Infinite loop in `umain()` executing various tasks:
   - Network processing (LwIP)
   - Communication protocols (IEC103, Modbus, PLC)
   - Protection algorithms
   - Data sampling and processing

### Task System
The system uses a cooperative multitasking approach with time-based scheduling:
- 500μs interrupt (`task_500us`) - High-frequency tasks
- 1ms interrupt - Medium-frequency tasks
- 5ms interrupt (`task_Fun`) - Protection algorithm processing
- Main loop - Communication and background tasks

### Communication Protocols
1. **IEC103** - Power system communication protocol on port 2404
2. **Modbus** - Industrial communication protocol
3. **PLC TCP Server** - Dedicated TCP server on port 2405 for PLC tasks
4. **Serial7000** - Proprietary serial protocol

### Key Hardware Features
- ADC sampling for electrical measurements
- GPIO control for inputs/outputs
- SPI communication with external devices
- Ethernet connectivity with LwIP stack
- RTC for timekeeping
- PWM and timer peripherals

## Development Guidelines

### Adding New Protection Modules
1. Create source files in `source/mods/`
2. Follow existing module patterns (e.g., `mod_overCur.c`)
3. Register module in `initModTask()` and `initModParm()`

### Adding Communication Features
1. Use existing TCP server infrastructure
2. For new protocols, consider adding dedicated server instances
3. Follow non-blocking communication patterns

### Hardware Integration
1. Use HAL drivers for peripheral access
2. Implement interrupt handlers in `Src/stm32f4xx_it.c`
3. Add initialization in appropriate `MX_*_Init()` functions