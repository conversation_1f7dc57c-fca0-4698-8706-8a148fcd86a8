#ifndef _MOD_RVSPWR_H_
#define _MOD_RVSPWR_H_

#include "task_queue.h"
#include "fun.h"

typedef struct
{
	char* dpName;
	unsigned char* flg_ptdx;//pt断线
	//输入信号
	int* P;
	
	//输出信号
	unsigned char qd_rvspwr;
	unsigned char op_rvspwr;
	unsigned char ovbi_rvspwr;		//软压板
	
	//输出模拟量
	
	//定值
	unsigned char vbi_rvspwr;		//软压板
	unsigned char setSwit_rvspwr;	//控制字
	int set_f_rvspwr;	//逆功率定值
	unsigned int set_t_rvspwr;	//逆功率时间
	int trip_matrix;
	
	//内部使用
	int timebuf_for_op;	//起动元件时间
	int timebuf_blk;
	int timebuf_op;
    int timebuf_zk;

	int set_f_rvspwr_qd;	//+0.05hz启动门槛
	unsigned char flg_blk;
	unsigned char flg_rvspwr_op_val;
	
}s_modRvsPwr;

int initModRvsPwr(s_modRvsPwr* dp, char* dpName);
int initModParmRvsPwr(s_modRvsPwr* dp);


#endif