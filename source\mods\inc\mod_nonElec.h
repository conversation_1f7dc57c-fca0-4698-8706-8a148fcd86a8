#ifndef _MOD_NONELEC_H_
#define _MOD_NONELEC_H_

#include "task_queue.h"
#include "fun.h"

typedef struct
{
	char* dpName;
	//输入信号
	unsigned char* flg_nonElec1_kr;	//非电量1开入
	unsigned char* flg_nonElec2_kr;	//非电量2开入

	//输出信号
	unsigned char flg_nonElec1_trip;	//非电量跳闸1启动
	unsigned char flg_nonElec2_trip;	
	unsigned char trip_nonElec_1;	//非电量跳闸1
	unsigned char trip_nonElec_2;
	
	//输出模拟量
	
	//定值
	unsigned char setSwit_nonElec1_trip;//非电量跳闸1
	unsigned char setSwit_nonElec2_trip;
	unsigned int set_t_nonElec_1;	//非电量时间1
	unsigned int set_t_nonElec_2;	//非电量时间2
	int trip_matrix1; //跳闸矩阵
	int trip_matrix2; //跳闸矩阵
	//内部
	int timebuf_trip_1;
	int timebuf_trip_2;
    int timebuf_zk1;
    int timebuf_zk2;

}s_modNonElec;
int initModNonElec(s_modNonElec* dp,char* dpName);
int initModParmNonElec(s_modNonElec* dp);



#endif