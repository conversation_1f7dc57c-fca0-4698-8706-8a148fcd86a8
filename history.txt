[25-05-21 17:09] init: 新建项目

[25-05-22 20:24] feat: 由cubemx生成第一版

[25-05-22 20:38] feat: 添加source，CMAKE

[25-05-23 11:03] feat: 根据硬件调整引脚

[25-05-23 15:37] feat: 修改source

[25-05-23 17:15] feat: 修改CubeMX

[25-05-25 14:15] feat: 更改mian函数

[25-05-25 23:53] feat: 更改source

[25-05-26 09:45] feat: 新增GPIO定义

[25-05-26 11:17] feat: 新增30个开入开出和电源开出的驱动

[25-05-27 17:07] fix: 修复频率采集定时器指定错误

[25-05-30 16:43] fix: 修改输入频率为25MHZ

[25-06-03 11:38] feat: 添加运行灯、改中断向量地址
1、修复编码问题
2、添加运行灯逻辑
3、删除多余的关于led灯和lcd屏逻辑

[25-06-03 14:57] fix: 修复编译错误
1、修改gpio、SPI模块名称、与cubemx区分
2、修复编译错误

[25-06-03 17:09] fix: 修改下载设置
1、修改Jlink设置
2、修复乱码问题，当main等文件中使用GBK编码，且有中文字符注释时，使用cubemx工程重新生成代码时，会将代码文件转换成UTF-8并使其乱码，所以将这部分代码文件直接转换成UTF-8

[25-06-03 17:26] fix: 修改下载设置
1、cubemx会修改keil工程设置

[25-06-04 13:31] fix: 主频现在为180，需修改定时器预分频器

[25-06-04 15:16] fix: 修改sct，修复跳转至 0xFFFFFFFE AAAAr2,sp,#0x2A8 错误

[25-06-04 16:21] fix: 修复不能点亮运行灯的问题
1、GPIO的定义不是由CubeMX生成，需要在gpio_xsj里自行定义

[25-06-04 16:26] fix: 暂时不检测定值错误

[25-06-05 09:22] feat: 新增在407上开发完的PLC模块

[25-06-05 15:08] feat: 更新PLC库

[25-06-05 18:16] feat: 更新通信点表

[25-06-05 18:17] feat: 更新通信点表，PLC测试

[25-06-06 08:32] feat: 添加在F407上开发的励磁模块

[25-06-06 09:04] fix: 更新PLC库文件

[25-06-06 10:29] fix: 简化不必要的判断

[25-06-06 13:49] feat: 修改开入开出逻辑
1、有两个开出在电源板上，开出16没用

[25-06-06 13:51] Merge branch 'dev' into PLC
# Conflicts:
#	source/mods/REF_TABLES.c

[25-06-06 14:20] feat: 根据通信点表更新PLC引用表

[25-06-06 15:02] feat: 新增setBiById、setAnaById、setAnaPointById接口
1、新增setBiById、setAnaById、setAnaPointById接口函数，用于PLC设置开出，测试与引用表交互正常
2、注意模拟量交互时，需注意保留位数

[25-06-06 15:23] feat: 测试设置开入模入

[25-06-06 17:13] feat: 励磁接口处理

[25-06-08 23:44] feat: 接口处理

[25-06-09 14:03] test: 测试PLC库获取SOE变量类型（动作/告警）

[25-06-09 15:32] feat: 定值接口1

[25-06-09 17:12] feat: 定值接口2

[25-06-09 17:22] feat: 定值接口3

[25-06-09 17:27] feat: 升级PLC库

[25-06-09 23:39] feat: 定值接口4

[25-06-10 17:06] feat: SOE逻辑处理

[25-06-10 19:31] fix: 修复全局变量中的字符指针的内容一直变的问题
1、由赋值函数中将局部变量地址赋值给全局变量地址导致

[25-06-10 20:14] fix: 修复串口接口BUG，修改测试程序

[25-06-11 17:18] feat: 整理头文件

[25-06-12 15:11] feat: 整理接口

[25-06-12 15:20] feat: 新增引用表接口（从PLC分支）

[25-06-12 15:23] Merge branch 'dev' into PLC

[25-06-12 15:35] Merge branch 'dev' into 励磁CPU通信

[25-06-14 10:13] fix: 修改调试程序，修改UART处理
1、MAX485芯片需要通过置使能引脚来改变通信方向

[25-06-14 15:15] feat: 新增运行灯状态切换功能，调整定时器中断逻辑
1. 增加 `stm_runled_Toggle` 用于运行灯状态切换
2. 定时器任务增加 DEBUG 宏条件控制
3. 修改测试框架

[25-06-14 15:18] Merge branch 'dev' into PLC
# Conflicts:
#	Src/main.c
#	source/platform/inc/setting.h

[25-06-14 15:34] fix: 修复励磁数据发送接受错误问题

[25-06-14 15:36] Merge branch 'dev' into 励磁CPU通信
# Conflicts:
#	source/platform/inc/setting.h

[25-06-20 11:13] feat: 修改工程设置、修复网络问题

[25-06-24 17:13] feat: 添加X13606的Modbus模块

[25-06-24 19:19] feat: 排序、适配底层接口

[25-06-24 20:40] feat: 增加引用表

[25-06-24 22:30] feat: 修改开关量处理

[25-06-24 23:14] feat: 修改模拟量处理

[25-06-26 14:34] fix: 定值处理

[25-06-26 15:37] fix: 1、去除定时器3，不能设置20ms定时器，会导致网络连接不上 2、外部FLASH驱动新增函数接口 3、修复打印失效（485使能）

[25-06-26 16:06] Merge branch 'Modbus触摸屏' into 屏幕和励磁

[25-06-26 16:08] Merge branch 'refs/heads/励磁CPU通信' into 屏幕和励磁
# Conflicts:
#	MDK-ARM/X13606_CUBEMX_GD32F470ZGT6.uvguix.luhao
#	MDK-ARM/X13606_CUBEMX_GD32F470ZGT6/X13606_CUBEMX_GD32F470ZGT6.map
#	MDK-ARM/X13606_CUBEMX_GD32F470ZGT6/X13606_CUBEMX_GD32F470ZGT6_X13606_CUBEMX_GD32F470ZGT6.dep
#	source/include/xsj_lib.h

[25-06-30 16:38] feat: 修复103通信问题

[25-07-01 15:30] feat: 新增励磁模拟量数据处理功能

[25-07-01 15:37] fix: 重构部分代码，定时器任务、模入定义、开出逻辑
1、定时器5的任务放在 task_500us 函数内
2、Network_Staus_flg必须开出来，因为103任务在检测它
3、修改模入端口变量的一些定义
4、开出逻辑修改

[25-07-01 15:46] feat: 增加Modbus定值组、模拟量、开关量及控制表的引用定义，增加测试标志，Modbus的暂存数组需要增加栈大小

[25-07-01 15:47] feat: 调整系统定值表和通信定值表，模拟量、开关量表

[25-07-01 16:03] Merge branch '屏幕和励磁' into dev
# Conflicts:
#	Src/lwip.c

[25-07-01 16:12] Merge branch 'PLC' into dev
# Conflicts:
#	CMakeLists.txt
#	MDK-ARM/JLinkLog.txt
#	MDK-ARM/X13606_CUBEMX_GD32F470ZGT6.uvguix.luhao
#	MDK-ARM/X13606_CUBEMX_GD32F470ZGT6.uvoptx
#	MDK-ARM/X13606_CUBEMX_GD32F470ZGT6.uvprojx
#	MDK-ARM/X13606_CUBEMX_GD32F470ZGT6/X13606_CUBEMX_GD32F470ZGT6.lnp
#	MDK-ARM/X13606_CUBEMX_GD32F470ZGT6/X13606_CUBEMX_GD32F470ZGT6.map
#	MDK-ARM/X13606_CUBEMX_GD32F470ZGT6/X13606_CUBEMX_GD32F470ZGT6_X13606_CUBEMX_GD32F470ZGT6.dep
#	source/include/xsj_lib.h
#	source/main_task/umain.c
#	source/mods/PARA_GROUPS.c
#	source/mods/REF_TABLES.c

[25-07-01 17:28] feat: 修改引用表格式、工程设置

[25-07-02 11:19] feat: 分离PLC模拟量和开关量引用表

[25-07-02 15:39] feat: Modbus、103应用表整理

[25-07-03 15:34] feat: 励磁和103接口处理

[25-07-03 15:35] feat: PLC开出时需要打开启动继电器

[25-07-03 15:35] feat: Modbus增加类型处理

[25-07-03 16:11] feat: 添加告警出口

[25-07-04 14:14] temp

[25-07-07 17:26] temp

[25-07-08 15:55] feat: 修改并测试开出逻辑

[25-07-08 17:30] feat: Modbus出口传动测试完成

[25-07-09 14:19] fix: 修复励磁模拟量采集问题、定值表需要和Modbus小数点配置相同

[25-07-09 15:39] feat: 修改开入接口，测试完成

[25-07-10 14:32] feat: 合并AD驱动测试成功，但是由于RAM占用较大，需要处理，否则不能正常运行

[25-07-10 16:22] temp: 待PLC修改，此处需要多分配RAM

[25-07-11 09:09] feat: 引入移交的保护模块

[25-07-11 13:14] feat: 修改工程和CMakeLists，修复编译错误，屏蔽同期模块，I0、U0不用删去，只是不再使用

[25-07-11 17:23] feat: 删除不需要的大电流闭锁模块、删除一些模块的软压板、修改保护模块定值表

[25-07-14 13:31] feat: 1、修改项目设置，增加RAM，2、增加过电压告警，3、继续修改定值

[25-07-15 10:47] feat: 添加版本信息显示

[25-07-15 14:34] feat: 修改自定义遥控

[25-07-15 18:06] feat: 更新PLC库

[25-07-15 20:47] feat: 开出PLC任务，修改BI_NUM

[25-07-16 11:16] fix: 更新PLC库

[25-07-16 20:46] feat: 1、Ux单独dig值，2、修复Ux通道取错，3、修改保护驱动出口逻辑，延长出口传动200ms，4、修复遥控投入错误，5、PLCM区双向传输，6、频率采集需要*2，7、增加PLC自定义变量

[25-07-18 11:23] test

[25-07-18 11:23] fix: 添加ETH中断，修复偶尔的通信中断问题

[25-07-18 11:23] test: 103励磁定值处理

[25-07-18 11:23] feat: 合并远程升级代码

[25-07-21 11:48] feat: 修改cubemx项目

[25-07-21 11:48] feat：添加发动机转速、转速百分比

[25-07-21 11:48] feat：rtc测试

[25-07-21 16:18] feat：plc测试功能添加

[25-07-22 10:02] feat：添加同期模块

[25-07-22 11:14] fix：修复同期定值只能正确读写前4个定值的错误

[25-07-22 11:32] test：新增同期模块PLC测试接口

[25-07-22 13:27] test：新增3次谐波计算

[25-07-22 16:46] feat: 同期模块PLC接口调试成功

[25-07-23 13:06] feat: 合并调试完成后的同期模块

[25-07-23 13:11] feat: 1、修改具体的动作参数，2、新增相序异常告警

[25-07-23 13:21] feat: 1、增加通信地址定值，2、修改通信点表

[25-07-23 13:49] feat: 1、添加告警出口

[25-07-24 11:50] feat: 1、PLCSOE接口可能还需调试更改

[25-07-24 14:04] feat: 1、通信点表核对修改

[25-07-25 17:25] fix: 1、修复励磁定值读取错误

[25-07-28 15:40] fix: 1、修复励磁定值写错误

[25-07-29 13:45] fix: 1、励磁机组参数定值需注意小数点

[25-07-29 13:46] feat: 1、同期模块展示变量需注意

[25-07-29 13:51] feat: 1、plcSOE需要和自定义变量一一对应

[25-07-29 16:46] fix: 修复采样跳动的问题，注意整形除法

[25-07-29 17:19] fix: 调整电压DIG值

[25-07-29 18:02] fix: 测试整改3次谐波计算

[25-07-29 19:04] feat: 修改PLCSOE表

[25-07-30 09:03] fix: 修复RTC问题，不能初始化重新设置时间

[25-07-30 09:04] fix: 调整开关量个数、PLC自定义变量个数

[25-07-30 15:36] docs: 注释

[25-07-31 09:13] feat: 增加一些自检信息

[25-07-31 09:13] feat: 更新PLC库，传输小数位数

[25-08-01 09:10] feat: 1、增加装置故障等各种自检信息、将告警IO翻转移至500us定时器（可以适当减慢）2、测试看门狗，需要在看门狗使能前翻转一次，不然使能时就会复位，3、还有几处告警地方需要待解决

[25-08-01 16:32] feat: Modbus增加出口状态

[25-08-04 11:15] feat: 串口103励磁定值功能修改测试

[25-08-04 11:47] feat: 修改task_500us，告警信号翻转设置间隔为2ms

[25-08-04 15:13] feat: 整改同期模块告警信息接口

[25-08-04 15:23] feat: 添加励磁开出，但需要接口赋值（或者测试）

[25-08-04 15:38] feat: 先把闭锁标志屏蔽，等后续测试再打开

[25-08-05 10:14] doc: 修改注释

[25-08-05 10:49] feat: 扩大测频范围，与原13606保持一致，35-75HZ

[25-08-05 16:48] feat: 励磁机组定值，小数位数确定，待测试

[25-08-05 16:49] todo: 测量电流电压与保护电流电压不是同一个dig值？待确认原因

[25-08-06 10:13] feat: 让103与PLC调试功能共存、103端口改为2405、PLC改为2404

[25-08-06 10:59] feat: 手动合并直流、温度采样

[25-08-06 14:15] feat: PLC传输只能传输整数，仿照Modbus缩放逻辑

[25-08-06 14:24] feat: 去除PLC数据变化检测，一直传输数据

[25-08-07 10:03] feat: 修复Modbus问题，定值没有进行负数处理，同时将定制组与PARA统一

[25-08-07 10:08] feat: 添加温度校准接口

[25-08-07 17:24] fix: 修复若干频率采样错误

[25-08-08 10:15] fix: 1、加快频率清零时间 2、开远控允许 3、修改Modbus通信定值组数量
