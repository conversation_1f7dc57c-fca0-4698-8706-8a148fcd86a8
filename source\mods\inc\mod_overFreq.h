#ifndef _MOD_OVERFREQ_H_
#define _MOD_OVERFREQ_H_

#include "task_queue.h"
#include "fun.h"

typedef struct
{
	char* dpName;
	
	//输入信号
	char* flg_abc;
	float* fm;
	int* u_max;
	
	//输出信号
	unsigned char qd_of;
	unsigned char op_of;
	unsigned char ovbi_of;		//软压板
	
	//输出模拟量
	
	//定值
	unsigned char vbi_of;		//软压板
	unsigned char setSwit_of;	//控制字
	int set_f_of;	//过频定值
	unsigned int set_t_of;	//过频时间
	int trip_matrix;
	
	//内部使用
	int timebuf_for_op;	//起动元件时间
	int timebuf_blk;
	int timebuf_op;
    int timebuf_zk;

	int set_f_of_qd;	//+0.05hz启动门槛
	unsigned char flg_blk;
	unsigned char flg_of_op_val;
	
}s_modOverFreq;

int initModOverFreq(s_modOverFreq* dp, char* dpName);
int initModParmOverFreq(s_modOverFreq* dp);


#endif