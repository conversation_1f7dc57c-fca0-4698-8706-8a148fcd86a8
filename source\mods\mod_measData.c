#include "mod_measData.h"

//均方根计算 一次只算一个通道
void vcMeasCalc(s_modMeasData* dp)
{
	float tmp = 0;
	int tmpAdj;
	int tmpSum = 0;
	unsigned short i,ptr,calNumI;
	float	calNumF;
	char chan;
	float f;
	float sndToPrim;
	int		N_0D1;

	
	dp->cur_chan = circAft(dp->cur_chan, CHAN_NUM, 1);	//下一个通道
	chan = dp->chan[dp->cur_chan];	//通道号
	
	//系数获取
	if(dp->cur_chan <= 2)	//i
	{
		sndToPrim = dp->sndToPrimIn;
		N_0D1 = dp->In_0D1;
	}
	else if(dp->cur_chan == 3)	//i0
	{
		sndToPrim = dp->sndToPrimI0;
		N_0D1 = dp->I0_0D1;
	}
	else if(dp->cur_chan <=6 )	//u
	{
		sndToPrim = dp->sndToPrimUn;	
		N_0D1 = dp->Un_0D1/2;
	}
	else if(dp->cur_chan == 7)	//u0
	{
		sndToPrim = dp->sndToPrimU0;	
		N_0D1 = dp->U0_0D1/2;
	}
	else if(dp->cur_chan == 8)	//ux
	{
		sndToPrim = dp->sndToPrimUx;
		N_0D1 = dp->Ux_0D1/2;
	}		
	
	//AD值累加
	if(chan != 8)
	{
		f = *dp->F1;
	}
	else
	{
		f = *dp->F2;
	}
	
	if(f > 55 || f < 45)
		calNumF =  SAMPPOINT;
	else
		calNumF = (SAMPPOINT*50)/(f);	
	
	calNumI = calNumF;
	for (i = 0; i < calNumI; i++)
	{
		ptr = circPre(dp->starptrNow, ADBUFLEN, 2 + i);
		tmp += g_adBuffer[chan][ptr] * g_adBuffer[chan][ptr];
	}
	
	ptr = circPre(dp->starptrNow, ADBUFLEN, 1);
	tmp += (calNumF - calNumI)* (g_adBuffer[chan][ptr]*g_adBuffer[chan][ptr]);
	tmp /= calNumF;
	//tmp *= SAMPPOINT;	
	
	dp->valuePtr[dp->cur_chan] = circAft(dp->valuePtr[dp->cur_chan], MEASAVG_BUF_LEN, 1);
	//精校计算
	//dp->meas_value[dp->cur_chan] = sqrt(tmp)*dp->coeff[dp->cur_chan]*1000 / 4.409;
	dp->meas_valueBuf[dp->cur_chan][dp->valuePtr[dp->cur_chan]] = sqrt(tmp)*dp->coeff[dp->cur_chan]*DEFAULT_DIV_VAL/0.7071;

	//小于0.1额定值采用傅里叶计算结果
	if(dp->meas_valueBuf[dp->cur_chan][dp->valuePtr[dp->cur_chan]] <= N_0D1)	
		dp->meas_valueBuf[dp->cur_chan][dp->valuePtr[dp->cur_chan]] = *dp->fourier_val[dp->cur_chan];

	for(i = 0; i < MEASAVG_BUF_LEN; i++)
	{
		tmpSum += dp->meas_valueBuf[dp->cur_chan][i];
	}
	dp->meas_value[dp->cur_chan] =  tmpSum/MEASAVG_BUF_LEN;
	
	//死区值判断
	if(dp->meas_value[dp->cur_chan] <= dp->deadZone[dp->cur_chan])
		dp->meas_value[dp->cur_chan] = 0;		
	
	//一次值计算
	dp->meas_value_prim[dp->cur_chan] = sndToPrim * dp->meas_value[dp->cur_chan];
	
}

void powerCalc(s_modMeasData* dp)
{
	int tmp_orig[6];
	float tmp_vector[6];
	float tmpCos, tmpSin;
	float coeff[6];
	unsigned short i, ptr1, ptr2, ptr3, ptr4;
	char ia, ib, ic, ua, ub, uc;

	memset(tmp_orig, 0, sizeof(tmp_orig));
	ia = dp->chan[0];
	ib = dp->chan[1];
	ic = dp->chan[2];
	ua = dp->chan[4];
	ub = dp->chan[5];
	uc = dp->chan[6];
	ptr1 = dp->starptr1;
	//ptr2 = dp->starptr2;
	ptr3 = dp->starptr3;
	//ptr4 = dp->starptr4;

	if (1)	 
	{	//三瓦法AD值计算
		for (i = 0; i < dp->Um_points; i++)
		{
			tmp_orig[0] += g_adBuffer[ia][ptr1] * g_adBuffer[ua][ptr1];	//ia ua
			tmp_orig[1] += g_adBuffer[ib][ptr1] * g_adBuffer[ub][ptr1];	//ib ub
			tmp_orig[2] += g_adBuffer[ic][ptr1] * g_adBuffer[uc][ptr1];	//ic uc
			tmp_orig[3] += g_adBuffer[ia][ptr3] * g_adBuffer[ua][ptr1] - g_adBuffer[ia][ptr1] * g_adBuffer[ua][ptr3];	//ia ua
			tmp_orig[4] += g_adBuffer[ib][ptr3] * g_adBuffer[ub][ptr1] - g_adBuffer[ib][ptr1] * g_adBuffer[ub][ptr3];	//ib ub
			tmp_orig[5] += g_adBuffer[ic][ptr3] * g_adBuffer[uc][ptr1] - g_adBuffer[ic][ptr1] * g_adBuffer[uc][ptr3];	//ic uc
			//指针移动
			ptr1++;
			ptr1 %= ADBUFLEN;
			ptr2++;
			ptr2 %= ADBUFLEN;
			ptr3++;
			ptr3 %= ADBUFLEN;
			ptr4++;
			ptr4 %= ADBUFLEN;
		}
		
		//死区判断
		if(dp->meas_value[0] == 0 || dp->meas_value[4] == 0)	//IA UA 为0
		{
			tmp_orig[0] = 0;
			tmp_orig[3] = 0;
		}
		if(dp->meas_value[1] == 0 || dp->meas_value[5] == 0)	//IB UB 为0
		{
			tmp_orig[1] = 0;
			tmp_orig[4] = 0;
		}
		if(dp->meas_value[2] == 0 || dp->meas_value[6] == 0)	//IC UC 为0
		{
			tmp_orig[2] = 0;
			tmp_orig[5] = 0;
		}		
		
		//精校修正
		coeff[0] = dp->coeff[0] * dp->coeff[4]*DEFAULT_DIV_VAL;
		coeff[1] = dp->coeff[1] * dp->coeff[5]*DEFAULT_DIV_VAL;
		coeff[2] = dp->coeff[2] * dp->coeff[6]*DEFAULT_DIV_VAL;
		
		tmp_vector[0] = tmp_orig[0]*dp->cos_jd[0] - tmp_orig[3]*dp->sin_jd[0];
		tmp_vector[1] = tmp_orig[1]*dp->cos_jd[1] - tmp_orig[4]*dp->sin_jd[1];
		tmp_vector[2] = tmp_orig[2]*dp->cos_jd[2] - tmp_orig[5]*dp->sin_jd[2];
		tmp_vector[3] = (tmp_orig[0]*dp->sin_jd[0] + tmp_orig[3]*dp->cos_jd[0])/2;
		tmp_vector[4] = (tmp_orig[1]*dp->sin_jd[1] + tmp_orig[4]*dp->cos_jd[1])/2;
		tmp_vector[5] = (tmp_orig[2]*dp->sin_jd[2] + tmp_orig[5]*dp->cos_jd[2])/2;
		
		tmpCos = tmp_vector[0]*coeff[0] + tmp_vector[1]*coeff[1] + tmp_vector[2]*coeff[2];
		tmpSin = tmp_vector[3]*coeff[0] + tmp_vector[4]*coeff[1] + tmp_vector[5]*coeff[2];
	}
	else
	{	//两瓦法AD值计算
		coeff[0] = dp->coeff[0]*DEFAULT_DIV_VAL;
		coeff[1] = dp->coeff[1]*DEFAULT_DIV_VAL;
		coeff[2] = dp->coeff[2]*DEFAULT_DIV_VAL;
		coeff[3] = dp->coeff[4]*DEFAULT_DIV_VAL;
		coeff[4] = dp->coeff[5]*DEFAULT_DIV_VAL;
		coeff[5] = dp->coeff[6]*DEFAULT_DIV_VAL;
		
		for (i = 0; i < dp->Um_points; i++)
		{
			tmp_orig[0] += g_adBuffer[ia][ptr1] *(g_adBuffer[ua][ptr1]*coeff[3] - g_adBuffer[ub][ptr1]*coeff[4]) 
							+ g_adBuffer[ia][ptr2] * (g_adBuffer[ua][ptr2]*coeff[3] - g_adBuffer[ub][ptr2]*coeff[4]);	//ia uab
			tmp_orig[1] -= g_adBuffer[ic][ptr1] * (g_adBuffer[ub][ptr1]*coeff[4] - g_adBuffer[uc][ptr1]*coeff[5]) 
							+ g_adBuffer[ic][ptr2] * (g_adBuffer[ub][ptr2]*coeff[4] - g_adBuffer[uc][ptr2]*coeff[5]);	//ic ubc
			tmp_orig[3] += g_adBuffer[ia][ptr3] * (g_adBuffer[ua][ptr1]*coeff[3] - g_adBuffer[ub][ptr1]*coeff[4])
							+ g_adBuffer[ia][ptr4] * (g_adBuffer[ua][ptr2]*coeff[3] - g_adBuffer[ub][ptr2]*coeff[4]);	//ia uab
			tmp_orig[4] -= g_adBuffer[ic][ptr3] * (g_adBuffer[ub][ptr1]*coeff[4] - g_adBuffer[uc][ptr1]*coeff[5]) 
							+ g_adBuffer[ic][ptr4] * (g_adBuffer[ub][ptr2]*coeff[4] - g_adBuffer[uc][ptr2]*coeff[5]);	//ic ubc
		}
		
		//死区判断
		if(dp->meas_value[0] == 0 || (dp->meas_value[4] == 0 && dp->meas_value[5] == 0))	//IA UA Ub 为0
		{
			tmp_orig[0] = 0;
			tmp_orig[3] = 0;
		}
		if(dp->meas_value[2] == 0 || (dp->meas_value[5] == 0 && dp->meas_value[6] == 0))	//IC Ub Uc 为0
		{
			tmp_orig[1] = 0;
			tmp_orig[4] = 0;
		}
		
		//精校修正
		tmp_vector[0] = tmp_orig[0]*dp->cos_jd[0] + tmp_orig[3]*dp->sin_jd[0];
		tmp_vector[1] = tmp_orig[1]*dp->cos_jd[1] + tmp_orig[4]*dp->sin_jd[1];
		tmp_vector[3] = -tmp_orig[0]*dp->sin_jd[0] + tmp_orig[3]*dp->cos_jd[0];
		tmp_vector[4] = -tmp_orig[1]*dp->sin_jd[1] + tmp_orig[4]*dp->cos_jd[1];
		
		tmpCos = tmp_vector[0]*coeff[0] + tmp_vector[1]*coeff[2];
		tmpSin = tmp_vector[3]*coeff[0] + tmp_vector[4]*coeff[2];
	}
	tmpCos /= (dp->Um_points * 0.50166);
	tmpSin /= (dp->Um_points * 0.50166);	
	
	dp->power_cosBuf[dp->powerPtr] = tmpCos;
	dp->power_sinBuf[dp->powerPtr] = tmpSin;
	dp->powerPtr = circAft(dp->powerPtr, MEASAVG_BUF_LEN, 1);
	tmpCos = 0;
	tmpSin = 0;
	for(i = 0; i < MEASAVG_BUF_LEN; i++)
	{
		tmpCos += dp->power_cosBuf[i];
		tmpSin += dp->power_sinBuf[i];
	}
	tmpCos /= MEASAVG_BUF_LEN;
	tmpSin /= MEASAVG_BUF_LEN;

    dp->power_cos = tmpCos;
	dp->power_sin = tmpSin;
	
	dp->power_cos_prim = tmpCos*dp->sndToPrimIn*dp->sndToPrimUn;
	dp->power_sin_prim = tmpSin*dp->sndToPrimIn*dp->sndToPrimUn;
	
	tmpCos /= 1000;
	tmpSin /= 1000;
	dp->power = sqrt(tmpCos*tmpCos + tmpSin*tmpSin);
	dp->cos = ((tmpCos < 0.35) && (tmpCos > -0.35)) ? 0:(tmpCos / dp->power)*DEFAULT_DIV_VAL;
}

void powerSumCalc(s_modMeasData* dp)
{
	if (dp->power_cos > 0)
		dp->powerSum[0] += (dp->power_cos * 1);		//功率 * 时间
	else
		dp->powerSum[2] -= (dp->power_cos * 1);

	if (dp->power_sin > 0)
		dp->powerSum[1] += (dp->power_sin * 1);
	else
		dp->powerSum[3] -= (dp->power_sin * 1);

	dp->power_wirte_cnt++;
	if (dp->power_wirte_cnt >= 60000)
	{
		dp->power_wirte_cnt = 0;
		//待添加 （写电度）需要掉电存储

	}
}

void runModMeasData(s_modMeasData* dp)
{
	if(g_FM1 > 55 || g_FM1 < 45)
		dp->Um_points = (SAMPPOINT);
	else
		dp->Um_points = ((SAMPPOINT*50)/(g_FM1) + 0.5);
	
	dp->starptrNow = g_adBufPtr;
	dp->starptr1 = circPre(dp->starptrNow, ADBUFLEN, dp->Um_points);			//往前推40个点
	//dp->starptr2 = circPre(dp->starptrNow, ADBUFLEN, dp->Um_points * 2);
	dp->starptr3 = circPre(dp->starptrNow, ADBUFLEN, dp->Um_points + dp->Um_points*3/4);
	//dp->starptr4 = circPre(dp->starptrNow, ADBUFLEN, dp->Um_points * 2 + +dp->Um_points * 3 / 4);
	
	//均方根计算幅值
	vcMeasCalc(dp);

	//两瓦法 三瓦法 计算功率
	powerCalc(dp);

	//计算电度
	//powerSumCalc(dp);

	return;
}

int initModMeasData(s_modMeasData* dp, char* dpName)
{
	dp->dpName = dpName;
	//注册开关量

	//注册模拟量
	regANA(dpName, "ia", 0, &dp->meas_value[0], DEFAULT_POINT, "A");
	regANA(dpName, "ib", 0, &dp->meas_value[1], DEFAULT_POINT, "A");
	regANA(dpName, "ic", 0, &dp->meas_value[2], DEFAULT_POINT, "A");
	regANA(dpName, "i0", 0, &dp->meas_value[3], DEFAULT_POINT, "A");
	regANA(dpName, "ua", 0, &dp->meas_value[4], DEFAULT_POINT, "V");
	regANA(dpName, "ub", 0, &dp->meas_value[5], DEFAULT_POINT, "V");
	regANA(dpName, "uc", 0, &dp->meas_value[6], DEFAULT_POINT, "V");
	regANA(dpName, "u0", 0, &dp->meas_value[7], DEFAULT_POINT, "V");
	regANA(dpName, "ux", 0, &dp->meas_value[8], DEFAULT_POINT, "V");
	
	regANA(dpName, "P", 0, &dp->power_cos, DEFAULT_POINT, "W");
	regANA(dpName, "Q", 0, &dp->power_sin, DEFAULT_POINT, "var");
    regANA(dpName, "cos", 0, &dp->cos, DEFAULT_POINT, 0);
	
	regANA(dpName, "ia_prim", 0, &dp->meas_value_prim[0], DEFAULT_POINT, "A");
	regANA(dpName, "ib_prim", 0, &dp->meas_value_prim[1], DEFAULT_POINT, "A");
	regANA(dpName, "ic_prim", 0, &dp->meas_value_prim[2], DEFAULT_POINT, "A");
	regANA(dpName, "i0_prim", 0, &dp->meas_value_prim[3], DEFAULT_POINT, "A");
	regANA(dpName, "ua_prim", 0, &dp->meas_value_prim[4], DEFAULT_POINT, "kV");
	regANA(dpName, "ub_prim", 0, &dp->meas_value_prim[5], DEFAULT_POINT, "kV");
	regANA(dpName, "uc_prim", 0, &dp->meas_value_prim[6], DEFAULT_POINT, "kV");
	regANA(dpName, "u0_prim", 0, &dp->meas_value_prim[7], DEFAULT_POINT, "kV");
	regANA(dpName, "ux_prim", 0, &dp->meas_value_prim[8], DEFAULT_POINT, "kV");
	
	regANA(dpName, "P_prim", 0, &dp->power_cos_prim, DEFAULT_POINT, "kW");
	regANA(dpName, "Q_prim", 0, &dp->power_sin_prim, DEFAULT_POINT, "kvar");	

	//获取电度（待添加）

	//加入队列
	addTaskLevel2(runModMeasData, dp);
	return SUCC;
}

int initModParmMeasData(s_modMeasData* dp)
{
	float angle_coeff;
	float in_coeff;
	//获取定值
	
	dp->sys_in_snd = getSetByDesc("sys_in_snd");
	dp->sys_in_prim = getSetByDesc("sys_in_prim");
	dp->sys_i0_snd = getSetByDesc("sys_i0_snd");
	dp->sys_i0_prim = getSetByDesc("sys_i0_prim");
	dp->sys_un_snd = getSetByDesc("sys_un_snd");
	dp->sys_un_prim = getSetByDesc("sys_un_prim");
	dp->sys_ux_snd = getSetByDesc("sys_ux_snd");
	dp->sys_ux_prim = getSetByDesc("sys_ux_prim");
	dp->sys_u0_snd = getSetByDesc("sys_u0_snd");
	dp->sys_u0_prim = getSetByDesc("sys_u0_prim");
	
	//一次值系数
	dp->sndToPrimIn = (float)dp->sys_in_prim/(float)dp->sys_in_snd;
	dp->sndToPrimI0 = (float)dp->sys_i0_prim/(float)dp->sys_i0_snd;
	dp->sndToPrimUn = (float)dp->sys_un_prim/(float)dp->sys_un_snd /1000;	
	dp->sndToPrimUx = (float)dp->sys_ux_prim/(float)dp->sys_ux_snd /1000;	
	dp->sndToPrimU0 = (float)dp->sys_u0_prim/(float)dp->sys_u0_snd /1000;		
	
	//0.15倍额定值门槛计算
	dp->In_0D1 = dp->sys_in_snd*15/100;
	dp->I0_0D1 = dp->sys_i0_snd*15/100;
	dp->Un_0D1 = dp->sys_un_snd*15/100;
	dp->U0_0D1 = dp->sys_u0_snd*15/100;
	dp->Ux_0D1 = dp->sys_ux_snd*15/100;
	
	if(dp->sys_in_snd != 10000)
		in_coeff = DIG_5A1A;
	else
		in_coeff = 1;	
	
	dp->coeff[0]   = (float)getSetByDesc("Ima_coeff")/ DIG_In /DEFAULT_DIV_VAL * in_coeff;
	dp->deadZone[0] = getSetByDesc("Ima_deadZone");
	
	dp->coeff[1]   = (float)getSetByDesc("Imb_coeff")/ DIG_In /DEFAULT_DIV_VAL * in_coeff;
	dp->deadZone[1] = getSetByDesc("Imb_deadZone");

	dp->coeff[2]   = (float)getSetByDesc("Imc_coeff")/ DIG_In /DEFAULT_DIV_VAL * in_coeff;
	dp->deadZone[2] = getSetByDesc("Imc_deadZone");	
	
	dp->coeff[3]   = (float)getSetByDesc("Im0_coeff")/ DIG_I0 /DEFAULT_DIV_VAL;
	dp->deadZone[3] = getSetByDesc("Im0_deadZone");	

	dp->coeff[4]   = (float)getSetByDesc("Uma_coeff") / DIG_Un /DEFAULT_DIV_VAL;
	dp->deadZone[4] = getSetByDesc("Uma_deadZone");

	dp->coeff[5]   = (float)getSetByDesc("Umb_coeff") / DIG_Un /DEFAULT_DIV_VAL;
	dp->deadZone[5] = getSetByDesc("Umb_deadZone");

	dp->coeff[6]   = (float)getSetByDesc("Umc_coeff") / DIG_Un /DEFAULT_DIV_VAL;
	dp->deadZone[6] = getSetByDesc("Umc_deadZone");

	dp->coeff[7]   = (float)getSetByDesc("Um0_coeff") / DIG_U0 /DEFAULT_DIV_VAL;
	dp->deadZone[7] = getSetByDesc("Um0_deadZone");

	dp->coeff[8]   = (float)getSetByDesc("Umx_coeff") / DIG_Ux /DEFAULT_DIV_VAL;
	dp->deadZone[8] = getSetByDesc("Umx_deadZone");	
	
	angle_coeff = (float)(getSetByDesc("angle_a")) * PI / (180*DEFAULT_DIV_VAL);
	dp->cos_jd[0] = cos(angle_coeff);
	dp->sin_jd[0] = sin(angle_coeff);
	
	angle_coeff = (float)(getSetByDesc("angle_b")) * PI / (180*DEFAULT_DIV_VAL);
	dp->cos_jd[1] = cos(angle_coeff);
	dp->sin_jd[1] = sin(angle_coeff);
	
	angle_coeff = (float)(getSetByDesc("angle_c")) * PI / (180*DEFAULT_DIV_VAL);
	dp->cos_jd[2] = cos(angle_coeff);
	dp->sin_jd[2] = sin(angle_coeff);	
	
	return SUCC;
}
