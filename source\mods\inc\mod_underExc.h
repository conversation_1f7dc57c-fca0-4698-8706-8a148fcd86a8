#ifndef _MOD_UNDEREXC_H_
#define _MOD_UNDEREXC_H_

#include "task_queue.h"
#include "fun.h"

typedef struct
{
	char* dpName;
	
	//输入信号
	char* flg_abc;
	float* fm;
	int* u_max;
	
	//输出信号
	unsigned char qd_exc;
	unsigned char op_exc;
	unsigned char ovbi_exc;		//软压板
	
	//输出模拟量
	
	//定值
	unsigned char vbi_exc;		//软压板
	unsigned char setSwit_exc;	//控制字
	int set_f_exc;	//励磁低电压定值
	unsigned int set_t_exc;	//失磁时间
	int trip_matrix;
	
	//内部使用
	unsigned char op_exc_pre;
	int timebuf_for_op;	//起动元件时间
	int timebuf_blk;
	int timebuf_op;
    int timebuf_zk;

	int set_f_exc_qd;	//+0.05hz启动门槛
	unsigned char flg_blk;
	unsigned char flg_exc_op_val;
	
}s_modUnderExc;

int initModUnderExc(s_modUnderExc* dp, char* dpName);
int initModParmUnderExc(s_modUnderExc* dp);


#endif