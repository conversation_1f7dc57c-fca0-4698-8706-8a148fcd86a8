#include "mod_rvsPwr.h"
/*逆功率保护*/

void runModRvsPwr(s_modRvsPwr* dp)
{
	unsigned char flg_con, flg_tmp;
    unsigned char flg_qd;

	int P;
	P = -*(dp->P);
	//投退条件
	flg_con = dp->setSwit_rvspwr;
	
	flg_tmp = flg_con & ((*dp->flg_ptdx)^0x1);

	if((P>dp->set_f_rvspwr_qd) && flg_tmp)
	{
		flg_qd = 1;
	}
	else
	{
		flg_qd = 0;
	}

	//功率大于逆功率定值	
	dp->qd_rvspwr = extRelay(flg_qd,&dp->timebuf_zk,500);


	//动作
	overRelay(&(dp->flg_rvspwr_op_val), &P, dp->set_f_rvspwr, &(dp->timebuf_for_op), 0.95, 1);
	dp->op_rvspwr = delayRelay(dp->flg_rvspwr_op_val & dp->qd_rvspwr,&(dp->timebuf_op),dp->set_t_rvspwr);
	
	return;
}

int initModRvsPwr(s_modRvsPwr* dp, char* dpName)
{
	dp->dpName = dpName;
	
	//注册开关量
	regBI(dpName, "op_rvspwr", 0, &dp->op_rvspwr);	//动作信号
	// regBI(dpName, "ovbi_rvspwr", 0, &dp->ovbi_rvspwr);	
	//注册模拟量
	
	//注册启动元件
	regQdElement(&dp->qd_rvspwr);
	
	//注册动作元件
	regTripElement(&dp->op_rvspwr, &dp->trip_matrix);
	
	//加入队列
	addTaskLevel2(runModRvsPwr, dp);
	
	return SUCC;
	
}
int initModParmRvsPwr(s_modRvsPwr* dp)
{
	//获取定值
	
	// dp->vbi_rvspwr = getSetByDesc("vbi_rvspwr");	//软压板
	// dp->ovbi_rvspwr = dp->vbi_rvspwr;
	dp->setSwit_rvspwr = getSetByDesc("setSwit_rvspwr");	//控制字
	dp->set_f_rvspwr = getSetByDesc( "set_f_rvspwr");	//逆功率定值
	dp->set_f_rvspwr_qd = dp->set_f_rvspwr*95/100;	//0.95门槛
	dp->set_t_rvspwr = getSetByDesc( "set_t_rvspwr"); //逆功率时间
	dp->trip_matrix = getSetByDesc("trip_matrix_rvspwr");
	return SUCC;
	
}