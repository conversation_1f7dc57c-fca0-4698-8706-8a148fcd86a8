#ifndef _MOD_NOVOL_H_
#define _MOD_NOVOL_H_
#include "task_queue.h"
#include "fun.h"
typedef struct
{
	char* dpName;//失压：no-volttage
	
	//输入信号
	int* uab;
	//unsigned char* orig_bi_kkj;
	unsigned char* orig_bi_twj;
	int* u_max;
	
	//输出信号
	unsigned char qd_nv;
	unsigned char qd_nv2;
	unsigned char op_nv;
	unsigned char ovbi_nv;		
	
	//输出模拟量
	
	//定值
	unsigned char vbi_nv;		//软压板
	unsigned char setSwit_nv;	//控制字
	int set_u_nv;				//失压定值
	unsigned int set_t_nv;		//失压时间
	int trip_matrix;
	//内部变量
	int timebuf_uvon;
	int timebuf_uvon2;
	int timebuf_qd;
	int timebuf_qd2;
	int timebuf_qdDly;
	int timebuf_qdDly2;
	int timebuf_dly;
	unsigned char flg_nv_qd_val;
	unsigned char flg_nv_qd_val2;
	unsigned char uv_on;
}s_modNoVo1;

int initModNoVol(s_modNoVo1* dp, char* dpName);
int initModParmNoVol(s_modNoVo1* dp);

#endif