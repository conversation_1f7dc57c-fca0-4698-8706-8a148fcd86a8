#ifndef _MOD_NEGOVERCUR_H_
#define _MOD_NEGOVERCUR_H_
#include "task_queue.h"
#include "fun.h"
typedef struct
{
	char* dpName;
	
	//输入信号
	int* i2;
	
	//输出信号
	unsigned char qd_neg_oc;
	unsigned char op_neg_oc;
	//unsigned char ovbi_neg_oc;

	//输出信号
	//unsigned char vbi_neg_oc;	//软压板
	unsigned char setSwit_neg_oc; //控制字
	int set_i_neg_oc;	//负序过流定值
	unsigned int set_t_neg_oc;	//负序过流时间定值
	int	trip_matrix;
	
	//内部变量
	int timebuf_qd;
	int timebuf_qd2;
	int timebuf_op;
	int timebuf_dly;
    int timebuf_zk;

	int qd_i_neg;
	unsigned char flg_neg_oc_qd_val;
	unsigned char flg_neg_oc_op_val;
	unsigned char flg_neg_oc_op_val2;

	
}s_modNegOverCur;

int initModNegOverCur(s_modNegOverCur* dp,char* dpName);
int initModParmNegOverCur(s_modNegOverCur* dp);

#endif