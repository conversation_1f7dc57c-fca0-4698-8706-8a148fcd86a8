#include "mod_noVol.h"
//失压保护
void runModNoVol(s_modNoVo1* dp)
{
	unsigned char flg_con,flg_tmp;
	
	//启动
	flg_con = dp->setSwit_nv & dp->vbi_nv;	//投退条件
	//启动小于1.05倍最大相间电压值
	underRelay(&(dp->flg_nv_qd_val),dp->u_max,dp->set_u_nv,&(dp->timebuf_qd),1.05,1);//连续3次
	underRelay(&(dp->flg_nv_qd_val2),dp->u_max,dp->set_u_nv*1.05,&(dp->timebuf_qd2),1.0,1);
	//动作
	dp->uv_on |= fDelayRelay(*dp->u_max > (dp->set_u_nv * 1.05), &dp->timebuf_uvon, 0);
	flg_tmp = fDelayRelay(dp->op_nv ^ 0x01, &dp->timebuf_uvon2, 200);
	dp->uv_on = dp->uv_on & flg_con & flg_tmp & ((*dp->orig_bi_twj) ^ 0x01);
	dp->qd_nv = extRelay(dp->flg_nv_qd_val  & dp->uv_on,&(dp->timebuf_qdDly) ,200);
	dp->qd_nv2 = extRelay(dp->flg_nv_qd_val2  & dp->uv_on,&(dp->timebuf_qdDly2) ,500);
	//延时
	dp->op_nv = fDelayReturnRelay(dp->qd_nv,&(dp->timebuf_dly),dp->set_t_nv,200);

}

int initModNoVol(s_modNoVo1* dp,char* dpName)
{
	dp->dpName = dpName;
	//注册开关量
	regBI(dpName, "op_nv", 0, &dp->op_nv);		//动作信号
	regBI(dpName, "ovbi_nv", 0, &dp->ovbi_nv);		
	//注册模拟量
	
	//注册启动元件
	regQdElement(&dp->qd_nv2);
	
	//注册动作元件
	regTripElement(&dp->op_nv, &dp->trip_matrix);
	
	//加入队列
	addTaskLevel2(runModNoVol,dp);
	return SUCC;
	
}

int initModParmNoVol(s_modNoVo1* dp)
{
	//获取定值
	
	dp->vbi_nv = getSetByDesc("vbi_nv");//软压板
	dp->ovbi_nv = dp->vbi_nv;
	dp->setSwit_nv = getSetByDesc("setSwit_nv");		//控制字
	dp->set_u_nv = getSetByDesc("set_u_nv");		//过失电压
	dp->set_t_nv = getSetByDesc("set_t_nv");		//过压时间
	dp->trip_matrix = getSetByDesc("trip_matrix_nv");
	return SUCC;
}