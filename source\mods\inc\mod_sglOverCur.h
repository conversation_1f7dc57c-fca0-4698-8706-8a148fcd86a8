#ifndef _MOD_SGLOVERCUR_H_
#define _MOD_SGLOVERCUR_H_
#include "task_queue.h"
#include "fun.h"
typedef struct
{
	char* dpName;
	
	//输入信号
	int* i_30;

	int* i_30_half;	
	
	//输出信号
	unsigned char qd_sgl_oc;
	unsigned char op_sgl_oc;
	unsigned char op_sgl_oc_half;
	//unsigned char ovbi_sgl_oc;
	
	//输出信号
	//unsigned char vbi_sgl_oc;	//软压板
	unsigned char setSwit_sgl_oc; //控制字
	//unsigned char setSwit_sgl_i_zc;	//零序自产控制字
	int set_i_sgl_oc;	//零序过流定值
	unsigned int set_t_sgl_oc;	//零序过流时间定值
	int	trip_matrix;
	
	//内部变量
	int timebuf_qd;
	int timebuf_qd2;
	int timebuf_op;
	int timebuf_op_half;
	int timebuf_dly;
	int timebuf_dly_half;
    int timebuf_zk;

	int qd_i_sgl;
	unsigned char flg_sgl_oc_qd_val;
	unsigned char flg_sgl_oc_op_val;
	unsigned char flg_sgl_oc_op_val2;
	unsigned char flg_sgl_oc_op_val_half;

	
}s_modSglOverCur;

int initModSglOverCur(s_modSglOverCur* dp,char* dpName);
int initModParmSglOverCur(s_modSglOverCur* dp);

#endif