# Git版本历史提取并按照指定格式排列生成txt文件
import subprocess
import os

output_file = "history.txt"

if os.path.exists(output_file):
    os.remove(output_file)

try:
    # 获取并直接按时间逆序输出Git日志
    logs = subprocess.check_output(
        ['git', 'log', '--pretty=format:[%cd] %s%n%b', '--date=format:%y-%m-%d %H:%M', '--reverse'],
        encoding='utf-8'
    )

    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(logs)
    print(f"日志提取完成，结果已保存到 {output_file}")
except subprocess.CalledProcessError as e:
    print("获取 Git 日志失败，请确认是否在 Git 仓库目录中运行脚本。")
